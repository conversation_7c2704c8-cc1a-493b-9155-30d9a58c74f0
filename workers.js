
	define("workers/response/index.js", function(require, module, exports, window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){ 			
"use strict";var e=0,r=1,a=worker.getFileSystemManager?worker.getFileSystemManager():null,t=worker.createSharedArrayBuffer;worker.onMessage((function(o){var s=o.type,i=o.payload;if(s===r){var n=i.filePath,f=i.data,p=f;i.isSharedBuffer&&(p=f.buffer),a.writeFile({filePath:n,data:p,success:function(){worker.postMessage({type:r,payload:{isok:!0,filePath:n}})},fail:function(e){worker.postMessage({type:r,payload:{isok:!1,filePath:n,err:e}})}})}if(s===e){var l=i.systemInfo,u=l.platform,d=l.version,y="android"===u.toLocaleLowerCase(),c=function(e,r){return e.split(".").map((function(e){return e.padStart(2,"0")})).join("")>=r.split(".").map((function(e){return e.padStart(2,"0")})).join("")}(d,"8.0.18");worker.postMessage({type:e,payload:{supportWorkerFs:y&&!!a&&c,supportSharedBuffer:y&&!!t}})}})); 
 			});
 	